var express = require('express')
var utils = require('../common/utils')
var queries = require('./queries_select')
var db = require('../common/db')
var multer = require('multer')
var moment = require('moment')
var sharp = require('sharp')
var fs = require('fs')
var crypto = require('crypto')
var router = express.Router()
var ertlh = require('./thirdparty/ertlh')
const path = require('path')
const Excel = require('exceljs')
const axios = require('axios')
const FormData = require('form-data');
const filetype = require('./filetype');
const logger = require('./logger');
const events = require('./events');
const {jwtSignUser, authMiddleware} = require('./auth')

// FIDO2 WebAuthn imports
const {
  generateRegistrationOptions,
  verifyRegistrationResponse,
  generateAuthenticationOptions,
  verifyAuthenticationResponse,
} = require('@simplewebauthn/server')

var upload = multer({dest: 'uploads/'})

router.get('/events/:clientId', authMiddleware, events.handler);
// curl -H "Content-Type: application/json" -d "{\"pwd\":\"evErith!nk\",\"cmd\":\"info\"}" https://simperum.disperakim.jatengprov.go.id/api/events-cmd
// curl -H "Content-Type: application/json" -d "{\"pwd\":\"evErith!nk\",\"cmd\":\"info\", \"userId\":\"10140007\", \"text\":\"anda diduga melakukan hacking terhadap sistem\"}" https://simperum.disperakim.jatengprov.go.id/api/events-cmd
// curl -H "Content-Type: application/json" -d "{\"pwd\":\"evErith!nk\",\"cmd\":\"logout\", \"userId\":\"08110012\"}" https://simperum.disperakim.jatengprov.go.id/api/events-cmd
// curl -H "Content-Type: application/json" -d "{\"pwd\":\"evErith!nk\",\"cmd\":\"notification\", \"userId\":\"1\", \"title\":\"Test Notification\", \"text\":\"Detail Notification\"}" https://simperum.disperakim.jatengprov.go.id/api/events-cmd
router.post('/events-cmd', (req, res) => {
  if (req.body && req.body.pwd == 'evErith!nk') {
    let cmd = {...req.body, pwd: undefined}
    events.notify(cmd, {type: cmd.type || 'command'})
    res.send({success: true})
  }
  else {
    res.send({success: false})
  }
});

router.get('/health', async (req, res) => {
  try {
    // let r = await db.checkDeadlock()
    // if (r) {
    //   res.send({ success: false, error: 'deadlock' })
    // }
    let r = await db.query('show processlist')
    if (r && r.length > 15) {
      res.send({success: false, error: 'too many connections'})
      return
    }
  } catch (ex) {
    res.send({success: false, error: ex.message})
    return
  }
  res.send({success: true})
});

router.get('/monitoring', async (req, res) => {
  try {
    let r = await db.checkDeadlock()
    if (r) {
      req.app.locals.telegram.sendAdmin(r)
    }
    r = await db.exec('Arch_SelMonitoring')
    if (r && r.length) {
      req.app.locals.telegram.sendAdmin(JSON.stringify(r))
    }
  } catch (ex) {
    req.app.locals.telegram.sendAdmin(ex.trace)
  }
  res.send({success: true})
});

// const pdf2img = require('pdf-img-convert');
// const tesseract = require('tesseract.js');
// const MiniSearch = require('minisearch')
// router.post('/verify', authMiddleware, async (req, res) => {
//   if (!req.body.fileUrl.match(/\.pdf$/)) {
//     res.send({ success: false, message: 'saat ini hanya mendukung file pdf' })
//     return
//   }
//   try {
//     let fnm = req.body.fileUrl.match(/(\w+).pdf$/)
//     if (!fnm) {
//       res.send({ success: false, message: 'file tidak didukung' })
//       return
//     }
//     let fileName = 'verify' + fnm[1]
//     let minis = new MiniSearch({
//       fields: ['text'], // fields to index for full-text search
//       storeFields: ['text'] // fields to return with search results
//     })

//     let fileUrl = req.body.fileUrl
//     if (process.env.NODE_ENV === 'development') {
//       if (!fs.existsSync(fileUrl)) fileUrl = 'https://simperum.disperakim.jatengprov.go.id/' + fileUrl
//     }

//     var images = await pdf2img.convert(fileUrl, { width: 1000 });
//     const worker = await tesseract.createWorker('ind');
//     let verify = req.body.verify
//     let documents = []
//     for (let i = 0; i < images.length; i++) {
//       fs.writeFileSync(`tmp/${fileName}-${i}.png`, images[i]);
//       const ret = await worker.recognize(`tmp/${fileName}-${i}.png`)
//       documents.push({ id: i, text: ret.data.text })
//     }
//     await worker.terminate();
//     minis.addAll(documents)

//     let i = 0
//     let errors = []
//     for (let k in verify) {
//       let v = verify[k]
//       if (v.text) {
//         const r = await minis.search(v.text, { fuzzy: 0.2 })
//         v.verified = r.length > 0 && r[0].queryTerms.length >= v.text.split(' ').length
//         console.log('searching for: ' + v.text + ' [' + v.verified + ']')
//         if (!v.verified && r) {
//           console.log(documents[3])
//           console.log(r)
//         }
//         // console.log(r)
//       }
//       if (!v.verified) {
//         errors.push(k)
//       }
//     }

//     // let errors = verify.filter(v => !v.verified).map(v => v.idx)
//     res.send({ success: errors.length == 0, errors })
//   } catch (ex) {
//     res.send({ success: false, errors: [] })
//   }
// });

const BLOCKED_IP = {}

router.post('/login', async (req, res) => {
  const ip = (
    req.headers['cf-connecting-ip'] ||
    req.headers['x-real-ip'] ||
    req.headers['x-forwarded-for'] ||
    req.connection.remoteAddress || ''
  )

  const deviceId = req.headers['x-device-id']

  if (BLOCKED_IP[ip] && BLOCKED_IP[ip].until && BLOCKED_IP[ip].until.isAfter(moment())) {
    res.send({
      success: false,
      message: `Terlalu banyak percobaan login, coba lagi dalam ${BLOCKED_IP[ip].until.diff(moment(), 'minutes')} menit`,
      type: 'error',
      data: null,
    })
    return
  } else if (BLOCKED_IP[ip] && BLOCKED_IP[ip].until && BLOCKED_IP[ip].until.isBefore(moment())) {
    BLOCKED_IP[ip].until = undefined
  }

  if (req.body._Username === undefined || req.body._Username?.match(/[^a-z0-9_.@\s]/i)) {
    console.log('attack: ', ip, req.body)
    if (!req.body._Username?.match(/^\d{2}\.\d{2}\.\d{2}\.\d{4}$/))
      req.app.locals.telegram.sendAdmin(`login attack: ${ip}: ` + JSON.stringify(req.body))

    if (!BLOCKED_IP[ip]) BLOCKED_IP[ip] = {failed: 1}
    BLOCKED_IP[ip].until = moment().add(10, 'minutes')
    res.send({
      success: false,
      message: 'Login Gagal',
      type: 'error',
      data: null,
    })
    return
  }

  var result = await db.exec('Arch_SelUserLogin', {...req.body, _ip: ip, _deviceId: deviceId})
  if (result && result.length && !result[0].ErrCode) {
    let token = jwtSignUser(result[0])
    if (BLOCKED_IP[ip]) delete BLOCKED_IP[ip]
    res
      .cookie('twj', token, {secure: true, sameSite: true, httpOnly: true})
      .send({
        success: true,
        data: result,
        token: token,
        type: 'array',
        message: 'Login Successfully.',
      })
  } else {
    if (!BLOCKED_IP[ip]) BLOCKED_IP[ip] = {failed: 1}
    else BLOCKED_IP[ip].failed++
    if (BLOCKED_IP[ip].failed > 10) {
      req.app.locals.telegram.sendAdmin(`${BLOCKED_IP[ip].failed} times failed login: ${ip}: ` + JSON.stringify(req.body))
      BLOCKED_IP[ip].until = moment().add(BLOCKED_IP[ip].failed * 10, 'minutes')
    } else if (BLOCKED_IP[ip].failed > 5) {
      req.app.locals.telegram.sendAdmin(`${BLOCKED_IP[ip].failed} times failed login: ${ip}: ` + JSON.stringify(req.body))
      BLOCKED_IP[ip].until = moment().add(6, 'minutes')
    }
    res.send({
      success: false,
      message: result.length && result[0].Message ? result[0].Message : '[!] Invalid Login',
      type: 'error',
      data: null,
    })
  }
})

// FIDO2 WebAuthn Configuration
const rpName = 'SIMPERUM'
const rpID = process.env.NODE_ENV === 'development' ? 'localhost' : 'simperum.disperakim.jatengprov.go.id'
const origin = process.env.NODE_ENV === 'development' ? 'http://localhost:8000' : 'https://simperum.disperakim.jatengprov.go.id'

// Temporary storage for challenges (in production, use Redis or database)
const challenges = new Map()

/*
DATABASE SCHEMA NEEDED FOR FIDO2:

CREATE TABLE FIDO2_Credentials (
  ID INT IDENTITY(1,1) PRIMARY KEY,
  Username NVARCHAR(100) NOT NULL,
  CredentialID NVARCHAR(MAX) NOT NULL,
  PublicKey VARBINARY(MAX) NOT NULL,
  Counter BIGINT NOT NULL DEFAULT 0,
  DeviceType NVARCHAR(50),
  BackedUp BIT DEFAULT 0,
  CreatedAt DATETIME2 DEFAULT GETDATE(),
  LastUsedAt DATETIME2,
  IsActive BIT DEFAULT 1
);

CREATE INDEX IX_FIDO2_Credentials_Username ON FIDO2_Credentials(Username);
CREATE INDEX IX_FIDO2_Credentials_CredentialID ON FIDO2_Credentials(CredentialID);

STORED PROCEDURES NEEDED:
- Arch_SavFIDO2Credential: Save new credential
- Arch_SelFIDO2Credentials: Get user's credentials
- Arch_SelFIDO2Credential: Get specific credential
- Arch_UpdFIDO2Counter: Update counter after authentication
*/

// FIDO2 Registration - Generate options
router.post('/fido2/register/begin', authMiddleware, async (req, res) => {
  const {_userId} = req.body

  if (!_userId) {
    return res.send({
      success: false,
      message: 'login is required'
    })
  }

  try {
    // TODO: Check if user exists in database
    const user = await db.exec('Arch_SelCurrentUser', req.body)
    if(!user.length) {
      return res.send({
        success: false,
        message: 'User not found'
      })
    }

    const options = await generateRegistrationOptions({
      rpName,
      rpID,
      userID: user[0].UserID,
      userName: user[0].Username,
      userDisplayName: user[0].FullName,
      attestationType: 'none',
      excludeCredentials: [], // TODO: Get existing credentials from database
      authenticatorSelection: {
        residentKey: 'preferred',
        userVerification: 'preferred',
        authenticatorAttachment: 'platform', // Prefer platform authenticators (biometrics)
      },
    })

    // Store challenge temporarily
    challenges.set(user[0].UserID, options.challenge)

    res.send({
      success: true,
      data: options
    })
  } catch (error) {
    console.error('FIDO2 registration begin error:', error)
    res.send({
      success: false,
      message: 'Failed to generate registration options'
    })
  }
})

// FIDO2 Registration - Verify response
router.post('/fido2/register/complete', authMiddleware, async (req, res) => {
  const {username, credential} = req.body

  if (!credential) {
    return res.send({
      success: false,
      message: 'Username and credential are required'
    })
  }

  try {
    const expectedChallenge = challenges.get(req.body._userId)
    if (!expectedChallenge) {
      return res.send({
        success: false,
        message: 'No challenge found for user'
      })
    }

    const verification = await verifyRegistrationResponse({
      response: credential,
      expectedChallenge,
      expectedOrigin: origin,
      expectedRPID: rpID,
    })

    // console.log(verification)

    if (verification.verified) {
      // TODO: Store credential in database
      await db.exec('Arch_SavCredential', {
        ...req.body,
        _CredentialID: {
          type:'Buffer', data: verification.registrationInfo.credentialID
        },
        _PublicKey: {
          type:'Buffer', data: verification.registrationInfo.credentialPublicKey
        },
        _Counter: verification.registrationInfo.counter,
        _DeviceType: verification.registrationInfo.credentialDeviceType,
        _BackedUp: verification.registrationInfo.credentialBackedUp,        
      }).catch(ex => console.error(ex))
      
      challenges.delete(req.body._userId)
      
      res.send({
        success: true,
        message: 'FIDO2 credential registered successfully'
      })
    } else {
      res.send({
        success: false,
        message: 'Failed to verify registration'
      })
    }
  } catch (error) {
    console.error('FIDO2 registration complete error:', error)
    res.send({
      success: false,
      message: 'Registration verification failed'
    })
  }
})

// FIDO2 Authentication - Generate options
router.post('/fido2/authenticate/begin', async (req, res) => {
  const {username} = req.body

  if (!username) {
    return res.send({
      success: false,
      message: 'Username is required'
    })
  }

  try {
    // TODO: Get user's credentials from database
    // const user = await db.exec('Arch_SelUser', {_Username: username})
    // if (!user.length) {
    //   return res.send({
    //     success: false,
    //     message: 'User not found'
    //   })
    // }

    const options = await generateAuthenticationOptions({
      rpID,
      allowCredentials: [], // TODO: Map from userCredentials
      userVerification: 'preferred',
    })

    // Store challenge temporarily
    challenges.set(username, options.challenge)

    res.send({
      success: true,
      data: options
    })
  } catch (error) {
    console.error('FIDO2 authentication begin error:', error)
    res.send({
      success: false,
      message: 'Failed to generate authentication options'
    })
  }
})

// FIDO2 Authentication - Verify response
router.post('/fido2/authenticate/complete', async (req, res) => {
  const {username, credential} = req.body

  if (!username || !credential) {
    return res.send({
      success: false,
      message: 'Username and credential are required'
    })
  }

  try {
    const expectedChallenge = challenges.get(username)
    if (!expectedChallenge) {
      return res.send({
        success: false,
        message: 'No challenge found for user'
      })
    }

    // Convert Base64URL credential.id to Buffer for database comparison
    const deviceId = req.headers['x-device-id']
    const credentialIdBuffer = Buffer.from(credential.id, 'base64url')

    // TODO: Get credential from database
    const dbCredential = await db.exec('Arch_SelCredential', {
      _deviceId: deviceId,
      _Username: username,
      _CredentialID: {type:'Buffer', data: credentialIdBuffer}
    })

    if (!dbCredential.length) {
      return res.send({
        success: false,
        message: 'Biometrik belum terdaftar'
      })
    }

    // Prepare authenticator object with proper data types
    const authenticator = {
      credentialID: credentialIdBuffer, // Use the converted Buffer
      credentialPublicKey: new Uint8Array(dbCredential[0].PublicKey), // Convert from database
      counter: dbCredential[0].Counter, // Get from database
    }

    const verification = await verifyAuthenticationResponse({
      response: credential,
      expectedChallenge,
      expectedOrigin: origin,
      expectedRPID: rpID,
      authenticator,
    }).catch(ex => {
      console.log(ex)
    })

    if (verification.verified) {
      // TODO: Update counter in database
      // await db.exec('Arch_UpdFIDO2Counter', {
      //   _Username: username,
      //   _CredentialID: credential.id,
      //   _Counter: verification.authenticationInfo.newCounter
      // })

      // Get user data for login
      // const result = await db.exec('Arch_SelUserLogin', {_Username: username})

      if (dbCredential && dbCredential.length && !dbCredential[0].ErrCode) {
        const token = jwtSignUser(dbCredential[0])
        challenges.delete(username)

        res
          .cookie('twj', token, {secure: true, sameSite: true, httpOnly: true})
          .send({
            success: true,
            data: {
              UserID: dbCredential[0].UserID,
              FullName: dbCredential[0].FullName,
              RolePositionID: dbCredential[0].RolePositionID,
              HomeUrl: dbCredential[0].HomeUrl,
              HasBiometric: dbCredential[0].HasBiometric,
            },
            token: token,
            type: 'array',
            message: 'FIDO2 Authentication successful',
          })
      } else {
        res.send({
          success: false,
          message: 'User not found or inactive'
        })
      }
    } else {
      res.send({
        success: false,
        message: 'Authentication verification failed'
      })
    }
  } catch (error) {
    console.error('FIDO2 authentication complete error:', error)
    res.send({
      success: false,
      message: 'Authentication failed'
    })
  }
})

router.get('/logout', async function (req, res) {
  var s = req.session
  if (s) s.user = undefined

  res
    .cookie('twj', null, {secure: true, sameSite: true, httpOnly: true, signed: true})
    .send({
      success: true,
      data: '/',
      type: 'redirect',
      message: 'Logged Out.',
    })
})

const email = require('../common/email')
const OTP_ASK = {}
router.post('/send-otp', async function (req, res) {
  let {_Username, _Purpose} = req.body
  let otp = utils.generateOTP()
  let msg = `SIMPERUM: ${otp} (batas waktu 5 menit)`
  const isEmail = _Username.match(/@/)

  let ip = (
    req.headers['cf-connecting-ip'] ||
    req.headers['x-real-ip'] ||
    req.headers['x-forwarded-for'] ||
    req.connection.remoteAddress || ''
  )

  // if (!_Phone.match(/^0[0-9]+$/)) {
  //   req.app.locals.telegram.sendAdmin(`otp attack: ${ip}: ` + _Phone)
  //   res.send({
  //     success: false,
  //     message: 'Nomor telepon tidak valid',
  //   })
  //   return
  // }

  if (!isEmail &&OTP_ASK[ip] && OTP_ASK[ip].askedAt?.isAfter(moment().subtract(1, 'minutes'))) {
    res.send({
      success: false,
      message: 'Terlalu banyak permintaan OTP, coba lagi dalam 1 menit',
    })
    return
  }

  if (!['login', 'reg-kkn'].includes(_Purpose)) {
    req.app.locals.telegram.sendAdmin(`otp purpose attack: ${ip}: ` + _Purpose)
    res.send({
      success: false,
      message: 'Akses Ilegal',
    })
    return
  }

  if(!isEmail)
    _Username = _Username.replace('+', '').replace('^0', '62')

  let ret = await db.exec('Arch_SavOTP', {_Username, _OTP: otp, _Purpose: _Purpose, _ip: ip})

  if (ret?.[0].ErrCode) {
    res.send({
      success: false,
      message: ret[0].Message,
    })
    return
  }
  if (isEmail) {
    let d = await email.sendMail({
      from: "SIMPERUM<<EMAIL>>", // sender address
      to: _Username, // receiver email
      subject: "OTP SIMPERUM", // Subject line
      text: msg,
    }).catch(err => {
      console.error(err)
      res.send({
        success: false,
        message: 'Gagal mengirim OTP',
      })
      return
    })
  } else {
    let d = await utils.sendWhatsapp(_Username, msg).catch(err => {
      console.error(err)
      res.send({
        success: false,
        message: 'Gagal mengirim OTP',
      })
      return
    })
  }

  OTP_ASK[ip] = {Phone: _Username, askedAt: moment()}
  setTimeout(() => {
    delete OTP_ASK[ip]
  }, 120000)

  res.send({
    success: true,
    message: `OTP telah dikirimkan ke ${isEmail ? 'email' : 'nomor'} anda`,
  })
})

router.get('/check-deadlock', async function (req, res) {
  let r = await db.checkDeadlock()
  res.send({success: true, data: {hasDeadlock: r}})
})

router.post('/log', async (req, res) => {
  logger.insertLog(req.body)
})

router.get('/resize', async (req, res) => {
  const tmp_path = req.query.path
  let errMsg = 'ok'
  try {
    const ss = sharp(tmp_path)
    await ss.resize(600)
      .toFile(tmp_path.replace(/\/ori\//, '/med/'))
      .catch(err => {
        if (fs.existsSync(tmp_path.replace(/\/ori\//, '/med/'))) fs.unlinkSync(tmp_path.replace(/\/ori\//, '/med/'))
        fs.symlinkSync(tmp_path, tmp_path.replace(/\/ori\//, '/med/'))
      })
    // await ss.resize(250).toFormat('jpeg').jpeg({force: true})
    //   .toFile(tmp_path.replace(/\/ori\//, '/small/'))
    //   .catch(err => errMsg = err.message)
    // await ss.resize(80).toFormat('jpeg').jpeg({force: true})
    //   .toFile(tmp_path.replace(/\/ori\//, '/tiny/'))
    //   .catch(err => errMsg = err.message)
    // res.render("complete");
  } catch (ex) {
    console.error(ex)
    res.send({
      success: false,
      message: ex.message,
    })
  }
  res.send({
    success: errMsg == 'ok',
    message: errMsg,
  })
})

const heicConvert = require('heic-convert');
const {isError} = require('util')
// Function to safely clean up temporary files
const cleanupTempFiles = (filePath) => {
  try {
    if (filePath && fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`Cleaned up temporary file: ${filePath}`);
    }
  } catch (err) {
    console.error(`Error cleaning up temporary file ${filePath}:`, err.message);
  }
};

const uploadfunction = async (req, res) => {
  // Track temporary files for cleanup in case of errors
  const tempFilesToCleanup = [];

  try {
    let ip = req.headers['cf-connecting-ip'] ||
      req.headers['x-real-ip'] ||
      req.headers['x-forwarded-for'] ||
      req.connection.remoteAddress || ''

    if (!req.file || !req.file.mimetype) {
      console.log('upload/no-file: ', ip, req)
      res.send({
        success: false,
        data: '',
        message: 'Tipe file tidak terdeteksi, hubungi admin.',
        type: 'error',
      })
      return
    }

    // Add the uploaded file to our cleanup list
    if (req.file && req.file.path) {
      tempFilesToCleanup.push(req.file.path);
    }

    let ext = ''
    if (req.file && req.file.originalname) {
      ext = req.file.originalname.match(/\.(\w+)$/)
      if (ext) ext = ext[1].toLowerCase()
    }
    let mimetype = req.file.mimetype
    if (['heic', 'heif'].includes(ext)) {
      mimetype = 'image/heic'
    }

    if (mimetype == 'application/octet-stream' || mimetype == 'text/html' ||
      !['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'heic', 'heif', 'jfif'].includes(ext)) {
      if (ext.match(/^ph/i)) {
        events.notify({cmd: 'logout', userId: req._session.userId}, {type: 'command'})
      }
      console.log(ip, req.file.mimetype, req.file.originalname, ext)
      if (!['mp4'].includes(ext)) {
        req.app.locals.telegram.sendAdmin(`attack: [${ip}|${req._session.userId}] ${req.file.mimetype} ${req.file.originalname}`)
      }
      res.send({
        success: false,
        data: '',
        message: 'File tidak didukung',
        type: 'error',
      })
    } else if (mimetype.match(/image/)) {
      let tmp_path = req.file.path

      let year_dir = moment().format('YYWW')
      let target_path = 'uploads/' + year_dir + '/ori/'
      if (!fs.existsSync('uploads/' + year_dir)) {
        fs.mkdirSync('uploads/' + year_dir)
        fs.mkdirSync('uploads/' + year_dir + '/ori/')
        fs.mkdirSync('uploads/' + year_dir + '/med/')
        fs.mkdirSync('uploads/' + year_dir + '/small/')
        fs.mkdirSync('uploads/' + year_dir + '/tiny/')
      }

      let exifData = await utils.getExifData(tmp_path).catch(err => {
        console.error(err)
        req.app.locals.telegram.sendAdmin('exif error [' + tmp_path + ']: ' + err.message)
      })
      let ext = '.jpg'
      if (mimetype == 'image/jpeg') ext = '.jpg'
      else if (mimetype == 'image/heic') {
        try {
          // convert
          const outBuffer = await heicConvert({
            buffer: fs.readFileSync(tmp_path), // HEIC buffer
            format: "JPEG", // Output format
          });
          const jpgPath = tmp_path + '.jpg';
          fs.writeFileSync(jpgPath, outBuffer);
          tempFilesToCleanup.push(jpgPath); // Add converted file to cleanup list
          tmp_path = jpgPath;
          ext = '.jpg';
        } catch (convErr) {
          console.error('HEIC conversion error:', convErr);
          // Clean up any temporary files
          tempFilesToCleanup.forEach(cleanupTempFiles);
          res.send({
            success: false,
            data: '',
            message: 'Error converting HEIC image: ' + convErr.message,
            type: 'error',
          });
          return;
        }
      }
      else ext = '.' + req.file.originalname.substr(-3)

      let filename = moment().format('DDHHmmssSSS') + ext

      let hash = crypto.createHash('sha1').setEncoding('hex');
      let src = fs.createReadStream(tmp_path)
      // let dest = fs.createWriteStream(target_path + filename)
      // src.pipe(dest)
      src.pipe(hash)
      src.on('end', async function () {
        const checksum = hash.read()
        // console.log(checksum)
        // let ff = await db.getFile(checksum)
        let ff = []
        if (ff.length) {
          res.send({
            success: true,
            data: '/uploads/' + ff[0].Url,
            meta: ff[0].ExifData ? JSON.parse(ff[0].ExifData) : null,
            message: 'File Uploaded!',
            type: 'string',
          })
        } else {

          let isError = null
          try {
            const ss = sharp(tmp_path, {failOnError: false}).withMetadata()
            let metadata = await ss.metadata()
            let maxW = metadata.width > metadata.height ? metadata.width : metadata.height

            let orientation = {
              6: -90,
            }
            ss.rotate(orientation[metadata?.orientation] || 0)
            await ss.resize({
              width: maxW > 2000 ? 2000 : maxW,
              height: maxW > 2000 ? 2000 : maxW,
              fit: sharp.fit.inside
            })
              .toFile(target_path + filename)
              .catch(err => {
                isError = err.message
                // fs.copyFileSync(tmp_path, target_path + filename)
              })
            await ss.resize(maxW > 600 ? 600 : maxW)
              .toFile(target_path.replace(/\/ori\//, '/med/') + filename)
              .catch(err => isError = err.message)
            await ss.resize(250)
              .toFile(target_path.replace(/\/ori\//, '/small/') + filename)
              .catch(err => isError = err.message)
            await ss.resize(80)
              .toFile(target_path.replace(/\/ori\//, '/tiny/') + filename)
              .catch(err => isError = err.message)
            // res.render("complete");
          } catch (ex) {
            // console.error(ex)
            isError = ex.message
          }
          if (isError) {
            console.log(isError)
            res.send({
              success: false,
              message: 'Gagal Upload! ' + isError,
              type: 'error',
            })
            return
          }

          ff = await db.insertFile(year_dir + '/ori/' + filename, checksum, req.file.originalname,
            exifData ? JSON.stringify(exifData) : exifData, req._session.userId)

          res.send({
            success: true,
            data: ff?.length ? '/uploads/' + ff[0].Url : '/' + target_path + filename,
            meta: exifData,
            message: 'File Uploaded!',
            type: 'string',
          })
          if ((!ff || !ff.length) && process.env.NODE_ENV != 'development') {
            req.app.locals.telegram.sendAdmin('Error when inserting into arch_uploads')
          }
        }
      })
      src.on('error', function (err) {
        console.error('Upload: ' + err.message)
        // Clean up any temporary files
        tempFilesToCleanup.forEach(cleanupTempFiles);
        res.send({
          success: false,
          data: '',
          message: err.message,
          type: 'error',
        })
      })
    } else {
      let tmp_path = req.file.path
      let ext = req.file.originalname.substr(
        req.file.originalname.lastIndexOf('.')
      )
      if (!filetype.isValid(req.file.path, ext)) {
        res.send({
          success: false,
          error: `Invalid ${ext.toUpperCase()} file`,
        })
        return
      }

      let year_dir = moment().format('YYWW')
      let filename = moment().format('DDHHmmssSSS') + ext
      let target_path = 'uploads/' + year_dir + '/'
      if (!fs.existsSync('uploads/' + year_dir)) {
        fs.mkdirSync('uploads/' + year_dir)
        fs.mkdirSync('uploads/' + year_dir + '/ori/')
        fs.mkdirSync('uploads/' + year_dir + '/med/')
        fs.mkdirSync('uploads/' + year_dir + '/small/')
        fs.mkdirSync('uploads/' + year_dir + '/tiny/')
      }

      let hash = crypto.createHash('sha1').setEncoding('hex');
      let src = fs.createReadStream(tmp_path)
      let dest = fs.createWriteStream(target_path + filename)
      src.pipe(dest)
      src.pipe(hash)
      src.on('end', async function () {
        const checksum = hash.read()
        let ff = await db.getFile(checksum)
        if (ff.length) {
          res.send({
            success: true,
            data: '/uploads/' + ff[0].Url,
            message: 'File Uploaded!',
            type: 'string',
          })
        } else {
          ff = await db.insertFile(year_dir + '/' + filename, checksum, req.file.originalname,
            null, req.body._userId)

          res.send({
            success: true,
            data: ff && ff.length ? '/uploads/' + ff[0].Url : '/' + target_path + filename,
            message: 'File Uploaded!',
            type: 'string',
          })
          if ((!ff || !ff.length) && process.env.NODE_ENV != 'development') {
            req.app.locals.telegram.sendAdmin('Error when inserting into arch_uploads')
          }
        }
      })
    }
  } catch (error) {
    console.error('Unhandled error in upload function:', error);
    // Clean up any temporary files
    tempFilesToCleanup.forEach(cleanupTempFiles);
    res.send({
      success: false,
      data: '',
      message: 'Internal server error during upload: ' + error.message,
      type: 'error',
    });
  }
}

// here

// Improved file cleanup function with proper error handling
const do_cleanres = async function (target_path) {
  try {
    if (!fs.existsSync(target_path)) {
      console.log(`Target path does not exist: ${target_path}`);
      return;
    }

    const files = fs.readdirSync(target_path);

    for (const f of files) {
      try {
        let file = target_path + f;

        if (fs.lstatSync(file).isDirectory()) {
          await do_cleanres(file + '/');
        } else {
          // Check if file is in use in the database
          const ret = await db.exec('Arch_CleanRes', {FileCheck: file});

          if (!ret || !ret.length) {
            console.log(`Deleting unused file: ${file}`);

            // Safe delete function with error handling
            const safeDelete = (filePath) => {
              try {
                if (fs.existsSync(filePath)) {
                  fs.unlinkSync(filePath);
                  console.log(`Successfully deleted: ${filePath}`);
                }
              } catch (deleteErr) {
                console.error(`Error deleting file ${filePath}:`, deleteErr.message);
              }
            };

            // Delete original and all resized versions
            safeDelete(file);
            safeDelete(file.replace('/ori/', '/med/'));
            safeDelete(file.replace('/ori/', '/small/'));
            safeDelete(file.replace('/ori/', '/tiny/'));
          }
        }
      } catch (fileErr) {
        console.error(`Error processing file ${target_path + f}:`, fileErr.message);
        // Continue with next file
      }
    }
  } catch (err) {
    console.error(`Error in do_cleanres for path ${target_path}:`, err.message);
  }
}

router.get('/cleanres/:folder?', async (req, res) => {
  try {
    // Get the year directory or use current week
    let year_dir = moment().format('YYWW')
    if (req.params.folder) year_dir = req.params.folder

    // Define target path
    const target_path = 'uploads/' + year_dir + '/ori/'

    // Clean up temporary uploads
    console.log('Cleaning up temporary uploads...');
    if (fs.existsSync('uploads/')) {
      const files = fs.readdirSync('uploads/');
      for (const file of files) {
        try {
          const filePath = 'uploads/' + file;
          if (
            fs.lstatSync(filePath).isFile() &&
            file.length === 32 &&
            !file.match(/\./)
          ) {
            console.log(`Cleaning up temporary upload: ${filePath}`);
            fs.unlinkSync(filePath);
          }
        } catch (err) {
          console.error(`Error cleaning up file uploads/${file}:`, err.message);
        }
      }
    }

    // Clean up temporary files
    console.log('Cleaning up temporary files...');
    if (fs.existsSync('tmp/')) {
      const tmpFiles = fs.readdirSync('tmp/');
      for (const file of tmpFiles) {
        try {
          const filePath = 'tmp/' + file;
          if (fs.lstatSync(filePath).isFile()) {
            console.log(`Cleaning up temporary file: ${filePath}`);
            fs.unlinkSync(filePath);
          }
        } catch (err) {
          console.error(`Error cleaning up file tmp/${file}:`, err.message);
        }
      }
    }

    // Run the main cleanup function on the target directory
    if (fs.existsSync(target_path)) {
      console.log(`Running cleanup on ${target_path}...`);
      await do_cleanres(target_path);
    } else {
      console.log(`Target path does not exist: ${target_path}`);
    }

    res.send({
      success: true,
      message: 'Cleanup completed successfully'
    });
  } catch (error) {
    console.error('Error in cleanres route:', error);
    res.send({
      success: false,
      message: 'Error during cleanup: ' + error.message
    });
  }
})

router.post('/upload', authMiddleware, upload.single('file'), uploadfunction)

router.post('/call/:sp', authMiddleware, async function (req, res) {
  try {
    let d = null
    if (req.params.sp.match(/_$/)) {
      let validation = await db.exec(req.params.sp + '1', req.body)
      if (validation.length && validation[0].ErrCode) {
        res.send({
          success: false,
          message: data && data.length && data[0].Message ? data[0].Message : '',
          type: 'error',
        })
        return
      }
      d = await db.exec(req.params.sp + '2', req.body)
    } else {
      d = await db.exec(req.params.sp, req.body)
    }

    let data = d
    if (req.body['__grid']) {
      data = {data: d}
    }

    if (data && data.length && data[0].Notification)
      events.notify(JSON.parse(data[0].Notification))

    if (data && data.length && data[0].ErrCode) {
      if (data && data.length && data[0].Error) {
        if (data[0].Error.match(/ER_/)) {
          if (!data[0].Error.match(/ER_BAD_NULL_ERROR/) &&
            !data[0].Error.match(/ER_DUP_ENTRY/)) {
            req.app.locals.telegram.sendAdmin(req.body._userId + ':' + data[0].Error)
          }
        }
      }
      res.send({
        success: false,
        data: data,
        type: 'error',
        message: data && data.length && data[0].Message ? data[0].Message : '',
      })
      return
    } else {
      res.send({
        success: true,
        data: data,
        type: 'array',
        message: data && data.length && data[0].Message ? data[0].Message : '',
      })
      return
    }
  } catch (ex) {
    console.log(ex)
    req.app.locals.telegram.sendAdmin('Error /call: ' + ex.message)
    res.send({
      success: false,
      data: 'Error',
      type: 'error',
      message: 'Error ' + ex.message,
    })
  }
})

router.post('/save/:table', authMiddleware, async function (req, res) {
  try {
    var d = await db.exec(req.params.sp, req.body)
    var data = d
    if (req.body['__grid']) {
      data = {data: d}
    }

    res.send({
      success: data && data.length && data[0].ErrCode ? false : true,
      data: data,
      type: 'array',
      message: data && data.length && data[0].Message ? data[0].Message : '',
    })
  } catch (ex) {
    res.send({
      success: false,
      data: 'Error',
      type: 'error',
      message: 'Error ' + ex.message,
    })
  }
})

router.post('/public/:sp', async function (req, res) {
  try {
    if (!req.params.sp.match(/^EVO/)) {
      res.send({
        success: false,
        message: 'Not authorized, Please Login Again.',
      })
    }
    var d = await db.exec(req.params.sp, req.body)
    var data = d
    if (req.body['__grid']) {
      data = {data: d}
    }

    res.send({
      success: data && data.length && data[0].ErrCode ? false : true,
      data: data,
      type: 'array',
      message: data && data.length && data[0].Message ? data[0].Message : '',
    })
  } catch (ex) {
    res.send({
      success: false,
      data: 'Error',
      type: 'error',
      message: 'Error ' + ex.message,
    })
  }
})

router.post('/select/:object', authMiddleware, async function (req, res) {
  try {
    var d = await db.query(queries.build(queries[req.params.object]))
    var data = d

    res.send({
      success: data && data.length && data[0].ErrCode ? false : true,
      data: data,
      type: 'array',
      message: data && data.length && data[0].Message ? data[0].Message : '',
    })
  } catch (ex) {
    res.send({
      success: false,
      data: 'Error',
      type: 'error',
      message: 'Error ' + ex.message,
    })
  }
})

router.post('/import', authMiddleware, async function (req, res) {
  // try {
  let {from, sp, to, _userId} = req.body
  if (from.match(/.xlsx$/) && to) {
    let fn = from.match(/\/(\d+).xlsx$/)
    fn = fn.length ? fn[1] : '0'
    const workbook = new Excel.Workbook()
    await workbook.xlsx.readFile(path.resolve('./' + from))
    let qq = []
    let cols = await db.getColumns(to)
    workbook.eachSheet((sheet) => {
      sheet.eachRow((row, rowIndex) => {
        // SKIP HEADER
        if (rowIndex > 1) {
          let values = row.values.splice(1, cols.length - 1)
          if (values[0] && values.length > 6) {
            values = values.map(v => {
              if (v.toString && v.toString().match(/\d\d\sGMT/)) {
                v = moment(v).format('YYYY-MM-DD')
              }
              return v
            })
            values.push(fn)
            qq.push(`('${values.join("','")}')`)
          }

          // if (v[0]) {
          //   v.push(fn)
          //   qq.push(`('${v.join("','").replace(' GMT+0700 (Western Indonesia Time)','')}')`)
          // }
        }
      })
    })
    let failed = []
    let sql = `INSERT INTO ${to} VALUES ${qq.join(',\n').replace(/,''/g, ',NULL')}`
    fs.writeFileSync('test.sql', sql)
    let dbres = await db.query(sql)
    if (dbres.length && dbres[0].length && dbres[0][0].ErrCode) {
      // console.log(sql)
      res.send({
        success: false,
        data: failed,
        type: 'Error',
        message: dbres[0][0].Error,
      })
    } else {
      if (sp) failed = await db.exec(sp, {Filename: fn, FileRef: fn, _userId})
      if (failed.length && failed[0].ErrCode) {
        res.send({
          success: false,
          data: failed[0],
          type: 'Error',
          message: 'Import Failed',
        })
      } else {
        res.send({
          success: true,
          data: failed,
          type: 'Success',
          message: 'Imported Successfully',
        })
      }
    }
  } else {
    res.send({
      success: false,
      data: 'Error',
      type: 'error',
      message: 'File type not suppported',
    })
  }
})

/**
 * Menyimpan berkas penyaluran ke aplikasi e-Bantuan BPKAD
 * @param {string} NoSurat - nomor surat
 * @param {string} Perihal - perihal surat
 * @param {string} TglSurat - tanggal surat - format DD-MM-YYYY
 * @param {string} KabupatenID - id kabupaten berdasarkan kode kemendagri
 */
router.post('/bpkad/submit', authMiddleware, async function (req, res) {

  // menyiapkan paramater
  var data = new FormData()
  data.append('no_surat', req.body.NoSurat)
  data.append('prihal', req.body.Perihal)
  data.append('tgl_surat', req.body.TglSurat.trim())
  data.append('kabupaten', req.body.KabupatenID)
  data.append('dokumen[47]', fs.createReadStream(req.body.SuratRekom.replace(/^\//, '')))
  data.append('dokumen[48]', fs.createReadStream(req.body.VerifikasiSKPD.replace(/^\//, '')))
  data.append('dokumen[49]', fs.createReadStream(req.body.Kwitansi.replace(/^\//, '')))
  data.append('dokumen[50]', fs.createReadStream(req.body.BukuRekening.replace(/^\//, '')))
  data.append('dokumen[51]', fs.createReadStream(req.body.KtpKades.replace(/^\//, '')))
  data.append('dokumen[68]', fs.createReadStream(req.body.LampiranRekom.replace(/^\//, '')))
  data.append('dokumen[89]', fs.createReadStream(req.body.BNBARTLH.replace(/^\//, '')))
  data.append('api-key', 'aWsrNEJyUlV6Y25LSitvSnArTXgxUT09')
  let isError = false
  let ret = await axios.post('https://bantuan.bpkad.jatengprov.go.id/api/disperakim', data, {
    // method: 'POST',
    // body: data,
    headers: {
      ...data.getHeaders(),
      'api-key': 'aWsrNEJyUlV6Y25LSitvSnArTXgxUT09',
    },
  }).catch(ex => {
    // console.log(ex.response.request)
    // console.log(ex.response.statusText)
    // console.log(ex.response.data)

    isError = true
    res.send({
      success: false,
      message: `Error while sending to BPKAD ${ex.response.data.message}`
    })
  })
  if (isError) {
    return
  }
  if (ret) console.log(ret.data)

  res.send({
    success: true,
    message: 'OK'
  })
})

router.post('/ertlh/update/:nik', authMiddleware, async function (req, res) {
  ertlh.insert(req.params.nik)
  res.send({
    success: true,
    data: 'This will take 20 secs to process',
  })
})

router.post('/ertlh/import/:kdwil', authMiddleware, async function (req, res) {
  let sqlinsert = 'REPLACE INTO raw_ertlh ('
  try {
    let kab = await db.query(`SELECT Kabupaten, Kecamatan , Kelurahan , KodeDagri FROM arch_varea av
              WHERE KelurahanID = ${req.params.kdwil};`)
    if (kab.length) {
      let data = await ertlh.read(kab[0].KodeDagri)
      if (data.length && data.forEach) {
        // console.log(`inserting ${data.length} data`)
        sqlinsert += Object.keys(data[0]).join(',') + ') VALUES '
        data.forEach((d) => {
          if (Number.isSafeInteger(parseInt(d.nik))) {
            d.nik = parseInt(d.nik)
            sqlinsert += (
              "('" +
              Object.values(d)
                .join('|')
                .replace(/'/g, '')
                .replace(/\|/g, "','") +
              "'),"
            )
              .replace(/,''/g, ',NULL')
              .replace(/,'\\',/g, ',NULL,')
              .replace(/\\','/g, "','")
          }
        })
        await db.query(sqlinsert.replace(/,$/, ';'))
        await db.exec('PRM_SavImportERTLH', {
          ...req.body,
          KodeDagri: kab[0].KodeDagri,
        })
        res.send({
          success: true,
          data: null,
          message: `${data.length} Data Imported Successfully`,
        })
      } else {
        res.send({
          success: false,
          data: null,
          message: `Tidak ada data untuk diimport`,
        })
      }
    } else {
      res.send({
        success: false,
        data: 'Wilayah tidak terdaftar',
      })
    }
  } catch (ex) {
    console.error(ex.message)
    // console.error(sqlinsert)
    res.send({
      success: false,
      data: 'Gagal Import',
    })
    req.app.locals.telegram.sendAdmin(ex.message)
  }
})

const checkToken = async function (token, raw) {
  try {
    let [hash, pubkey] = token.split('.')
    // console.log(hash, pubkey);
    var d = await db.exec('Arch_SelPubKey', {PubKey: pubkey})
    // console.log(raw);
    // console.log(
    //   hash,
    //   privkey,
    //   crypto
    //     .createHash("md5")
    //     .update(raw + d[0].privkey)
    //     .digest("hex")
    // );
    return (
      hash ==
      crypto
        .createHash('md5')
        .update(raw + d[0].privkey)
        .digest('hex')
    )
  } catch {
    return false
  }
}
router.get('/pub/:sp', async function (req, res) {
  try {
    let token = req.headers.authorization
    let ipAddrs = (
      req.headers['cf-connecting-ip'] ||
      req.headers['x-real-ip'] ||
      req.headers['x-forwarded-for'] ||
      req.socket.remoteAddress || ''
    );

    // Log API access
    await db.exec('Arch_InsApiLogs', {
      PubKey: token,
      RequestUrl: req.params.sp,
      RequestData: JSON.stringify({...req.query, ipAddrs})
    }).catch(logErr => {
      console.error('Error logging API access:', logErr.message);
    });
    if (await checkToken(token, req.url.replace('/pub/', ''))) {
      req.query.PubKey = token.split('.')[1]
      let d = await db.exec('PUB_' + req.params.sp, req.query)
      if (req.query.filetype === 'csv') {
        if (d.length) {
          let csv = Object.keys(d[0]).join(',')
          for (let i = 0; i < d.length; i++) {
            csv += Object.values(d[i]).join(',') + '\n'
          }
          res.send(csv)
        } else {
          res.send('')
        }
      } else {
        if (d.length) {
          let needParse = []
          for (let key in d[0]) {
            if (key.match(/^_json_/)) {
              needParse.push(key)
              // console.log(d[0][key])
            }
          }
          if (needParse.length) {
            d = d.map(o => {
              for (let key of needParse) {
                let newKey = key.replace(/^_json_/, '')
                o[newKey] = JSON.parse(o[key])
                delete o[key]
              }
              return o;
            })
          }
        }
        res.send({
          Success: true,
          Data: d,
          Type: 'array',
          Message: '',
        })
      }
    } else {
      res.send({
        Success: false,
        Data: 'Error',
        Type: 'error',
        Message: 'Error Invalid Token',
      })
    }
  } catch (ex) {
    res.send({
      Success: false,
      Data: 'Error',
      Type: 'error',
      Message: 'Error ' + ex.message,
    })
  }
})

// let d = await axios.get(`http://whatsapp:2984/send?to=${to.replace('+', '')}&message=SILAKON: ${otp}&key=er1ganteng`)

router.post('/pub/:sp', async function (req, res) {
  try {
    let token = req.headers.authorization
    let ipAddrs = (
      req.headers['cf-connecting-ip'] ||
      req.headers['x-real-ip'] ||
      req.headers['x-forwarded-for'] ||
      req.socket.remoteAddress || ''
    );
    let logs = db.exec('Arch_InsApiLogs', {
      PubKey: token,
      RequestUrl: req.params.sp,
      RequestData: JSON.stringify({...req.body, ipAddrs})
    })
    // if (await checkToken(token, req.url.replace('/pub/', ''))) {
    if (await checkToken(token, JSON.stringify(req.body))) {
      req.body.PubKey = token.split('.')[1]
      let d = await db.exec('PUB_' + req.params.sp, req.body)
      if (req.params.sp == 'SaveDataRTLH' && d.length) {
        ertlh.insert(d[0].NIK)
      }
      if (d.length) {
        let needParse = []
        for (let key in d[0]) {
          if (key.match(/^_json_/)) {
            needParse.push(key)
            // console.log(d[0][key])
          }
        }
        if (needParse.length) {
          d = d.map(o => {
            for (let key of needParse) {
              let newKey = key.replace(/^_json_/, '')
              o[newKey] = JSON.parse(o[key])
              delete o[key]
            }
            return o;
          })
        }
      }
      await logs
      res.send({
        Success: d.length && !d[0].ErrCode ? true : false,
        Data: d,
        Type: 'array',
        Message: d.length ? d[0].Message : 'Data Tidak Ditemukan',
      })
    } else {
      await logs
      res.send({
        Success: false,
        Data: 'Error',
        Type: 'error',
        Message: 'Error Invalid Token',
      })
    }
  } catch (ex) {
    res.send({
      Success: false,
      Data: 'Error',
      Type: 'error',
      Message: 'Error ' + ex.message,
    })
  }
})

module.exports = router
