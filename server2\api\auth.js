const jwt = require('jsonwebtoken')
const JWT_SECRET = 'ganteng'

const getAuthToken = req => {
  if (process.env.NODE_ENV == 'development') {
    const tokenHeader = req.headers.authorization || req.signedCookies['twj'] || req.cookies['twj']
    if (tokenHeader) {
      if (tokenHeader.startsWith('Bearer ')) {
        return tokenHeader.slice(7, tokenHeader.length)
      }
      return tokenHeader
    } else {
      // console.log(req.headers)
      return false
    }
  } else {
    return req.signedCookies['twj']
  }
}
  
const authMiddleware = (req, res, next) => {
  let ip = (
    req.headers['cf-connecting-ip'] ||
    req.headers['x-real-ip'] ||
    req.headers['x-forwarded-for'] ||
    req.connection.remoteAddress || ''
  )
  let deviceId = req.headers['x-device-id']
  req._session = {ip, deviceId}
  
  // if (!deviceId) {
  //   res.send({
  //     success: false,
  //     message: 'Not authorized, Please Login Again.',
  //   })
  //   return
  // }

  let token = getAuthToken(req)
  if(req.url.match(/^\/call\/EVO/)) {
    next()
  } else if(req.method == 'GET' && req.query?.xpass == 'fv9a9e7j') {
    next()
  } else if (token) {
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        // console.error(err.message)
        // next(err);
        res.send({
          success: false,
          errCode: 404,
          message: 'Not authorized, Please Login Again.',
        })
      } else {
        if (decoded) {
          // req.user = { id: decoded.userId };
          req.body._ip = ip
          req.body._userId = decoded.userId
          req.body._roleId = decoded.roleId
          req.body._deviceId = deviceId
          req._session = decoded
          req._session.ip = ip
          req._session.deviceId = deviceId
        }
        next()
      }
    })
  } else {
    res.send({
      success: false,
      errCode: 500,
      message: 'Not authorized, Please Login Again.',
    })
    return
  }
}
  
// router.use(authMiddleware);
  
const jwtSignUser = (user) => {
  const ONE_DAY = 60 * 60 * 24 // * 7 * 4 * 4;
  return jwt.sign({userId: user.UserID, roleId: user.RolePositionID}, JWT_SECRET, {
    expiresIn: ONE_DAY,
  })
}

module.exports = {
  getAuthToken, authMiddleware, jwtSignUser
}