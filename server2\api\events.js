module.exports = {
  clients: {},
  handler: (req, res, next) => {
    if (res.headersSent) return
    
    let {clientId} = req.params
    const headers = {
      'Content-Type': 'text/event-stream',
      'Connection': 'keep-alive',
      'Cache-Control': 'no-cache'
    };
    res.writeHead(200, headers);
        
    const data = `from server\n\n`;
        
    res.write(data);
        
    const cId = req.body._userId+':'+Date.now();
        
    const newClient = {
      id: cId,
      userId: req.body._userId,
      roleId: req.body._roleId,
      res
    };
        
    // console.log(`${cId} Connected`);
    module.exports.clients[cId] = newClient;
        
    res.on('close', () => {
      // console.log(`${cId} Connection closed`);
      // module.exports.clients = module.exports.clients.filter(client => client.id !== cId);
      delete module.exports.clients[cId]
    });
  },
  notify:(data, opt = {type:'notification'}) => {
    for (let clientId in module.exports.clients) {
      let client = module.exports.clients[clientId] 
      // for now send only for this role
      if(opt.type == 'command' || [1,2].includes(client.roleId))
        if(!opt.targetUserId || (opt.targetUserId && client.userId == opt.targetUserId))
          client.res.write(`event: ${opt.type}\ndata: ${JSON.stringify(data)}\n\n`)
    }
  },
}