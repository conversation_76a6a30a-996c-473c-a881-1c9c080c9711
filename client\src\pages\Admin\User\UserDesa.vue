<template>
  <Page title="Data Pengguna">
    <template v-slot:toolbar>
      <v-btn small @click="showRolesPage = true">+ Role</v-btn>
    </template>
    <div style="display: flex">
      <XSelect
        placeholder="Role"
        dbref="Arch.SelRolePosition"
        :dbparams="{}"
        :value.sync="roleId"
        width="190px"
      />
      <XSelect
        v-if="roleId == 9"
        placeholder="Kabupaten"
        dbref="PRM.SelPBDTCity"
        :dbparams="{}"
        :value.sync="kabupaten"
        width="190px"
        @change="UpdateKabupaten"
      />
      <XSelect
        v-if="roleId == 9"
        placeholder="Kecamatan"
        dbref="Arch.SelArea"
        :dbparams="{ ParentAreaID: kabupatenId }"
        :value.sync="kecamatan"
        width="190px"
      />
    </div>
    <Grid
      :datagrid.sync="users"
      dbref="Arch.User"
      :dbparams="{
        RolePositionID: roleId,
        Kabupaten: kabupaten,
        Kecamatan: kecamatan,
      }"
      style="height: calc(100vh - 150px)"
      class="dense"
      :disabled="true"
      :columns="[
        {
          name: 'Username',
          value: 'Username',
          width: '120px',
        },
        {
          name: 'Nama Lengkap',
          value: 'FullName',
          width: '350px',
        },
        {
          name: 'Phone',
          value: 'Phone',
          width: '200px',
          class: 'plain',
        },
      ]"
    >
      <template v-slot:row-IsActive="{ row }">
        <div center>
          <v-icon>
            {{
              row.IsActive ? 'mdi-checkbox-marked-outline' : 'mdi-crop-square'
            }}
          </v-icon>
        </div>
      </template>
      <template v-slot:row-Phone="{ row }">
        <!-- <nik-block v-if="row.Phone" :nik="row.Phone" /> -->
        <XInput type="text" :value.sync="row.Phone" @change="SavePhone(row)" />
      </template>
      <template v-slot:row-RolePositionName="{ row }">
        <v-btn
          text
          small
          color="primary"
          @click="ShowRoleAccess(row.RolePositionID)"
        >
          {{ row.RolePositionName }}
        </v-btn>
      </template>
      <template v-slot:row-AreaAccess="{ row }">
        <div center>
          <v-icon @click="ShowAreaAccess(row.UserID)">
            mdi-map-marker-path
          </v-icon>
        </div>
      </template>
    </Grid>
    <Modal
      id="modal-role-access"
      :show.sync="roleAccess"
      title="ROLE ACCESS"
      width="350px"
      @onSubmit="SubmitRoleAccess"
    >
      <RoleAccessPage :roleId="roleId" :page-data.sync="roleAccessData" />
    </Modal>
    <Modal
      id="modal-roles"
      :show.sync="showRolesPage"
      title="ROLES"
      @onSubmit="SubmitRoles"
    >
      <RolesPage :page-data.sync="rolesData" />
    </Modal>
    <Modal
      id="modal-roles"
      :show.sync="showAreaAccess"
      title="AREA AKSES"
      @onSubmit="SubmitAreaAccess"
    >
      <AreaAccessPage :userId="userId" :page-data.sync="areaAccessData" />
    </Modal>
  </Page>
</template>
<script>
import RoleAccessPage from './RoleAccess.vue'
import RolesPage from './Roles.vue'
import AreaAccessPage from './AreaAccess.vue'
import NikBlock from '../../../components/NikBlock.vue'

export default {
  components: {
    RoleAccessPage,
    RolesPage,
    AreaAccessPage,
    NikBlock,
  },
  data: () => ({
    roleAccess: false,
    showRolesPage: false,
    showAreaAccess: false,
    roleAccessData: [],
    rolesData: [],
    areaAccessData: [],
    roleId: null,
    userId: null,
    users: null,
    kabupaten: null,
    kabupatenId: null,
    kecamatan: null,
    forms: {},
  }),
  async mounted() {},
  methods: {
    UpdateKabupaten(val) {
      this.kabupatenId = val
    },
    ShowRoleAccess(roleId) {
      this.roleAccess = true
      this.roleId = roleId
    },
    ShowAreaAccess(userId) {
      this.showAreaAccess = true
      this.userId = userId
    },
    async SubmitRoles() {},
    async SavePhone(row) {
      let ret = await this.$api.call('Arch.UpdUserDesa', {
        UserID: row.UserID,
        Phone: row.Phone,
      })
      // if (ret.success) row.Phone = row.Phone
    },
    async SubmitRoleAccess() {
      let ret = this.$api.call('Arch.SavRoleAccess', {
        RolePositionID: this.roleId,
        XmlRoleAccess: this.roleAccessData,
      })
      if (ret.success) this.roleAccess = false
    },
    async SubmitAreaAccess() {
      let ret = await this.$api.call('Arch.SavUserArea', {
        UserID: this.userId,
        Remarks: this.areaAccessData
          .filter((a) => a.AllowAccess)
          .map((a) => a.AreaID)
          .join('|'),
      })

      if (ret.success) this.showAreaAccess = false
    },
  },
}
</script>
<style lang="scss">
#modal-role-access {
  .ui-table {
    height: 500px !important;
  }
}
</style>
