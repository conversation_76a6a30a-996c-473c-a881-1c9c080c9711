<template>
  <Modal
    title="VALIDASI DETAIL"
    :show.sync="x_show"
    :loading="loading"
    :saving="saving"
    :error="error"
    :errorAction="errorAction"
    :warning="warning"
    :showActions="!disabled"
    @onSubmit="Save"
    @error-action-yes="errorActionYes"
    @error-action-no="errorActionNo"
  >
    <template v-slot:left-action>
      <v-btn v-if="AllowDelete" text color="red" @click="Delete">Hapus</v-btn>
    </template>
    <div
      style="display: flex"
      :style="isMobile ? 'width:100vw;' : 'width:680px; height:570px;'"
      :class="isMobile ? '' : 'form-inline'"
    >
      <div style="width: 50px; color: #333" v-show="!isMobile">
        <div
          class="vert-tab"
          @click="tabId = 'Individu'"
          :class="{
            active: tabId == 'Individu',
          }"
        >
          <v-icon style="font-size: 36px">person</v-icon>
        </div>
        <div
          tab="Rumah"
          class="vert-tab"
          @click="tabId = 'Rumah'"
          :class="{
            active: tabId == 'Rumah',
          }"
        >
          <v-icon style="font-size: 36px">home</v-icon>
        </div>
        <div
          tab="Konstruksi"
          class="vert-tab"
          @click="tabId = 'Konstruksi'"
          :class="{
            active: tabId == 'Konstruksi',
          }"
        >
          <v-icon style="font-size: 36px">mdi-hammer-wrench</v-icon>
        </div>
        <div
          tab="Kesehatan"
          class="vert-tab"
          @click="tabId = 'Kesehatan'"
          :class="{
            active: tabId == 'Kesehatan',
          }"
        >
          <v-icon style="font-size: 36px">mdi-stethoscope</v-icon>
        </div>
        <div
          tab="Foto"
          class="vert-tab"
          @click="tabId = 'Foto'"
          :class="{
            active: tabId == 'Foto',
          }"
        >
          <v-icon style="font-size: 36px">collections</v-icon>
        </div>
        <!-- <div
          tab="FAQ"
          class="tab"
          @click="tabId = 'FAQ'"
          :class="{
            active: tabId == 'FAQ',
          }"
        >
          <v-icon style="font-size: 36px">mdi-account-question</v-icon>
        </div> -->
        <div
          v-if="igahp"
          tab="Lentera"
          class="vert-tab"
          @click="tabId = 'Lentera'"
          :class="{
            active: tabId == 'Lentera',
          }"
        >
          <!-- <v-icon style="font-size: 36px">mdi-coach-lamp</v-icon> -->
          <img
            src="/imgs/igahp.svg"
            height="32px"
            style="margin-top: 10px; filter: grayscale(100%); opacity: 0.5"
          />
        </div>
      </div>
      <div
        style="overflow: auto; border-left: 1px solid silver; margin-left: 8px"
        :style="
          isMobile ? 'width:calc(100vw - 40px);' : 'width:680px; height:100%;'
        "
      >
        <div v-show="tabId == 'Individu' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XInput label="NIK*" :value.sync="forms.NIK">
              <template v-slot:label>
                <div style="display: flex">
                  <div>NIK*</div>
                  <v-spacer />
                  <v-btn
                    x-small
                    :outlined="forms.TipeData < 200 || isBDT"
                    color="primary"
                    style="margin-top: 2px; margin-right: 8px"
                    @click="CheckDinsos"
                    :loading="loadingBDT"
                  >
                    {{ 200 > forms.TipeData || isBDT ? 'bdt' : 'check bdt' }}
                  </v-btn>
                </div>
              </template>
            </XInput>
            <XInput
              label="Nama*"
              :value.sync="forms.Nama"
              :disabled="forms.NewTipeData"
            />
            <XInput label="No. KK" :value.sync="forms.NoKK" />
            <XInput label="Alamat*" :value.sync="forms.Alamat" />
            <XInput label="No HP" :value.sync="forms.Phone" />
            <!-- <XInput label="Umur*" :value.sync="forms.Umur" /> -->
            <DatePicker
              label="Tanggal Lahir*"
              :value.sync="forms.TanggalLahir"
            />
            <XSelect
              label="Jns. Kelamin*"
              :value.sync="forms.JenisKelamin"
              :items="[
                { val: 'L', txt: 'Laki-Laki' },
                { val: 'P', txt: 'Perempuan' },
              ]"
            />
            <XSelect
              v-if="igahp"
              label="Agama*"
              :value.sync="forms.Agama"
              :items="[
                { val: '1', txt: 'Islam' },
                { val: '2', txt: 'Kristen' },
                { val: '3', txt: 'Katolik' },
                { val: '4', txt: 'Hindu' },
                { val: '5', txt: 'Budha' },
                { val: '6', txt: 'Konghucu' },
                { val: '7', txt: 'Lainnya' },
              ]"
            />
            <XSelect
              label="Sts. Perkawinan*"
              :value.sync="forms.Perkawinan"
              dbref="PRM.SelPerkawinan"
            />
            <XSelect
              label="Pendidikan*"
              :value.sync="forms.Pendidikan"
              dbref="PRM.SelPendidikan"
            />
            <XSelect
              label="Pekerjaan*"
              :value.sync="forms.Pekerjaan"
              dbref="PRM.SelPekerjaan"
            />
            <XInput label="No. NPWP" :value.sync="forms.NoNPWP" />
            <XSelect
              label="Penghasilan*"
              :value.sync="forms.Penghasilan"
              dbref="PRM.SelPenghasilan"
            />
            <Checkbox
              label="Memiliki Tabungan"
              :value.sync="forms.AdaTabungan"
            />
            <XSelect
              label="Mampu Swadaya*"
              :value.sync="forms.MampuSwadaya"
              :items="[
                { val: '1', txt: 'Mampu' },
                { val: '0', txt: 'Tidak Mampu' },
              ]"
            />
            <!-- <div class="info-red">
              *) Data harus diisi untuk bisa masuk ERTLH
            </div> -->
          </div>
        </div>
        <div v-show="tabId == 'Rumah' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XSelect
              label="Sts. Rumah*"
              :value.sync="forms.KepemilikanRumah"
              :items="[
                { val: 1, txt: 'Milik Sendiri' },
                { val: 2, txt: 'Kontrak/Sewa' },
                { val: 3, txt: 'Bebas Sewa' },
                { val: 4, txt: 'Dinas' },
                { val: 6, txt: 'Menumpang' },
                { val: 5, txt: 'Lainnya' },
              ]"
            />
            <XSelect
              label="Sts. Lahan*"
              :value.sync="forms.KepemilikanLahan"
              dbref="PRM.SelStatusTanah"
            />
            <XSelect
              label="Tanah Lain"
              :value.sync="forms.TanahLain"
              :items="[
                { val: '1', txt: 'Memiliki' },
                { val: '0', txt: 'Tidak Memiliki' },
              ]"
            />
            <XSelect
              label="Rumah Lain"
              :value.sync="forms.RumahLain"
              :items="[
                { val: '1', txt: 'Memiliki' },
                { val: '0', txt: 'Tidak Memiliki' },
              ]"
            />
            <XInput
              label="Luas Rumah*"
              type="number"
              :value.sync="forms.LuasRumah"
            />
            <XInput
              label="Jml. Penghuni*"
              type="number"
              :value.sync="forms.JmlPenghuni"
            />
            <XInput
              label="Jml. Keluarga*"
              type="number"
              :value.sync="forms.JmlKK"
            />
            <XLabel label="Sumber Bantuan" v-show="!igahp">
              <XSelect
                :value.sync="forms.Sumber"
                dbref="PRM.SelSumber"
                :dbparams="{ Kecuali: '2' }"
                height="150px"
                width="160px !important"
                style="margin-right: 10px"
              />
              <XSelect
                :items="limaTahun"
                :value.sync="forms.Tahun"
                placeholder="Tahun"
                width="80px !important"
              />
            </XLabel>

            <XSelect
              label="Kawasan*"
              :value.sync="forms.KawasanPerumahan"
              dbref="PRM.SelKawasan"
              height="120px"
            />
            <!-- <div class="info-red">
              *) Data harus diisi untuk bisa masuk ERTLH
            </div> -->
          </div>
        </div>
        <div v-show="tabId == 'Konstruksi' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XSelect
              label="Pondasi*"
              :value.sync="forms.AdaPondasi"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              label="Kondisi Kolom*"
              :value.sync="forms.KondisiKolom"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              label="Kondisi Balok*"
              :value.sync="forms.KondisiBalok"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              label="Kondisi Sloof"
              :value.sync="forms.KondisiSloof"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              label="Kondisi Plafon"
              :value.sync="forms.KondisiPlafon"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              label="Rangka Atap*"
              :value.sync="forms.KondisiRangka"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              label="Bahan Lantai*"
              :value.sync="forms.LantaiID"
              dbref="PRM.SelLantai"
            />
            <XSelect
              label="Kondisi Lantai*"
              :value.sync="forms.KondisiLantai"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              label="Bahan Dinding*"
              :value.sync="forms.DindingID"
              dbref="PRM.SelDinding"
            />
            <XSelect
              label="Kondisi Dinding*"
              :value.sync="forms.KondisiDinding"
              dbref="PRM.SelKondisi"
            />
            <XSelect
              label="Bahan Atap*"
              :value.sync="forms.AtapID"
              dbref="PRM.SelAtap"
              height="120px"
            />
            <XSelect
              label="Kondisi Atap*"
              :value.sync="forms.KondisiAtap"
              dbref="PRM.SelKondisi"
              height="95px"
            />
            <!-- <div class="info-red">
              *) Data harus diisi untuk bisa masuk ERTLH
            </div> -->
          </div>
        </div>
        <div v-show="tabId == 'Kesehatan' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <XSelect
              label="Jendela"
              :value.sync="forms.AdaJendela"
              :items="[
                { val: '1', txt: 'Ada Jendela' },
                { val: '0', txt: 'Tidak Ada' },
              ]"
            />
            <XSelect
              label="Ventilasi"
              :value.sync="forms.AdaVentilasi"
              :items="[
                { val: '1', txt: 'Ada Ventilasi' },
                { val: '0', txt: 'Tidak Ada' },
              ]"
            />
            <XSelect
              label="Sumber Air*"
              :value.sync="forms.SumberAir"
              dbref="PRM.SelSumberAir"
            />
            <XSelect
              label="Kmr Mandi/Jamban*"
              :value.sync="forms.KamarMandi"
              dbref="PRM.SelKamarMandi"
            />
            <XSelect
              label="Jenis Jamban"
              :value.sync="forms.JenisKloset"
              dbref="PRM.SelKloset"
            />
            <XSelect
              label="Jenis TPA"
              :value.sync="forms.JenisSeptikTank"
              dbref="PRM.SelSepticTank"
            />
            <XSelect
              label="Jarak Septik Tank*"
              :value.sync="forms.JarakSepticTank"
              :items="[
                { val: 0, txt: '&lt; 10m' },
                { val: 1, txt: '&gt;= 10m' },
              ]"
            />
            <XSelect
              label="Sumber Penerangan*"
              :value.sync="forms.Penerangan"
              dbref="PRM.SelPenerangan"
            />
            <XSelect
              label="BB Masak*"
              :value.sync="forms.BBMasak"
              :items="[
                { val: 1, txt: 'Listrik / Gas' },
                { val: 2, txt: 'Minyak Tanah' },
                { val: 3, txt: 'Arang / Kayu' },
                { val: 4, txt: 'Lainnya' },
              ]"
            />
            <!-- <div class="info-red">
              *) Data harus diisi untuk bisa masuk ERTLH
            </div> -->
          </div>
        </div>
        <div v-show="tabId == 'Foto' || isMobile">
          <div
            style="padding-left: 10px; width: 100%"
            :class="isMobile ? '' : 'form-inline'"
          >
            <div>
              <Map
                :ref="'map'"
                :lat.sync="forms.GeoLat"
                :lon.sync="forms.GeoLng"
                width="100%"
                height="378px"
                :searchbox="true"
                @change="onMapChange"
                v-if="show && !isMobile"
              />
            </div>
            <XLabel v-show="isMobile" label="Titik Lokasi">
              <XInput style="width: 50%" :value.sync="forms.GeoLat" />
              <XInput style="width: 50%" :value.sync="forms.GeoLng" />
            </XLabel>
            <v-btn
              color="primary"
              style="width: 100%; margin-bottom: 12px"
              v-show="isMobile"
              @click="getLocation"
            >
              <v-icon left>mdi-crosshairs-gps</v-icon>
              TITIK SAAT INI
            </v-btn>
            <!-- <v-btn @click="applyGpsFromPhoto" style="margin-top: -80px">
              Terapkan GPS berdasarkan foto
            </v-btn> -->
            <div>
              <Uploader
                label="FOTO DIRI*"
                :value.sync="forms.Profile"
                @change="updateMapLocation"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
              ></Uploader>
              <Uploader
                label="FOTO DEPAN*"
                :value.sync="forms.RumahDepan"
                @change="updateMapLocation"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
              >
              </Uploader>
              <Uploader
                label="FOTO SAMPING*"
                :value.sync="forms.RumahSamping"
                @change="updateMapLocation"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
              >
              </Uploader>
              <Uploader
                label="FOTO LAHAN"
                :value.sync="forms.Rumah0"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
              >
              </Uploader>
              <br />
              <Uploader
                label="ATAP 0%"
                :value.sync="forms.Atap0"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
              ></Uploader>
              <Uploader
                label="LANTAI 0%"
                :value.sync="forms.Lantai0"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
              >
              </Uploader>
              <Uploader
                label="DINDING 0%"
                :value.sync="forms.Dinding0"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
              >
              </Uploader>
              <Uploader
                label="JAMBAN 0%"
                :value.sync="forms.Jamban0"
                accept=".jpg,.jpeg,.jfif,.png,.heic,.jfif,.png,.heic"
              >
              </Uploader>
            </div>
          </div>
        </div>
        <div v-show="tabId == 'Lentera' || isMobile">
          <div style="padding: 10px" :class="isMobile ? '' : 'form-inline'">
            <div style="margin-bottom: 8px; font-weight: bold">
              PEMINATAN PROGRAM
            </div>
            <XSelect
              label="Berminat Pembiayaan"
              :value.sync="forms.AdaMinat"
              :items="[
                { val: '1', txt: 'Berminat' },
                { val: '2', txt: 'Tidak Berminat' },
              ]"
            />
            <XSelect
              label="Jenis Peminatan"
              :value.sync="forms.JenisMinat"
              :disabled="disableProgram"
              :items="[
                { val: '1', txt: 'Milik' },
                { val: '2', txt: 'Sewa' },
              ]"
            />
            <XSelect
              label="Program Pembiayaan"
              :value.sync="forms.ProgramPembiayaan"
              :disabled="disableProgram"
              :items="[
                { val: '1', txt: 'Pemilikan Rumah' },
                { val: '2', txt: 'Pembangunan Rumah' },
                { val: '3', txt: 'Perbaikan Rumah' },
              ]"
            />
            <XSelect
              label="Bank Pelaksana"
              :value.sync="forms.BankPelaksana"
              :disabled="disableProgram"
              :items="[
                { val: '1', txt: 'Bank BTN' },
                { val: '2', txt: 'Bank BRI' },
                { val: '4', txt: 'Bank Mandiri' },
                { val: '5', txt: 'Bank BNI' },
                { val: '6', txt: 'Bank Jateng' },
                { val: '7', txt: 'Bank BCA' },
                { val: '3', txt: 'Bank Lainnya' },
              ]"
            />
            <XSelect
              label="Besar Cicilan"
              :value.sync="forms.BesarCicilan"
              :disabled="disableProgram"
              :items="[
                { val: '1', txt: '< 500 ribu' },
                { val: '2', txt: '500 ribu - 750 ribu' },
                { val: '3', txt: '750 ribu - 1 juta' },
                { val: '4', txt: '> 1 juta' },
              ]"
            />
            <XSelect
              label="Tahun Rencana"
              :value.sync="forms.TahunRencana"
              :disabled="disableProgram"
              :items="[
                { val: '2025', txt: '2025' },
                { val: '2026', txt: '2026' },
                { val: '2027', txt: '2027' },
                { val: '0', txt: 'Belum Tahu' },
              ]"
            />
            <div style="margin: 10px 0 8px 0; font-weight: bold">
              KAWASAN IKLIM
            </div>
            <XLabel label="Sarana Transportasi">
              <div>
                <Checkbox
                  text="Angkutan Umum"
                  :value.sync="forms.AngkutanUmum"
                />
                <Checkbox text="Terminal" :value.sync="forms.Terminal" />
                <Checkbox text="Stasiun" :value.sync="forms.Stasiun" />
                <Checkbox text="Pasar" :value.sync="forms.Pasar" />
                <Checkbox text="Bank" :value.sync="forms.Bank" />
                <Checkbox text="Gerbang Tol" :value.sync="forms.GerbangTol" />
                <Checkbox text="SPBU" :value.sync="forms.SPBU" />
              </div>
            </XLabel>
            <XLabel label="Sarana Pendidikan">
              <div>
                <Checkbox text="TK" :value.sync="forms.TK" />
                <Checkbox text="SD" :value.sync="forms.SD" />
                <Checkbox text="SLTP" :value.sync="forms.SLTP" />
                <Checkbox text="SLTA" :value.sync="forms.SLTA" />
                <Checkbox text="Universitas" :value.sync="forms.Universitas" />
                <Checkbox text="Lainnya" :value.sync="forms.SekolahLainnya" />
              </div>
            </XLabel>
            <XLabel label="Tempat Ibadah">
              <div>
                <Checkbox text="Masjid" :value.sync="forms.Masjid" />
                <Checkbox text="Gereja" :value.sync="forms.Gereja" />
                <Checkbox text="Vihara" :value.sync="forms.Vihara" />
                <Checkbox text="Klenteng" :value.sync="forms.Klenteng" />
                <Checkbox text="Lainnya" :value.sync="forms.IbadahLainnya" />
              </div>
            </XLabel>
            <XSelect
              label="Genangan Air"
              :value.sync="forms.GenanganAir"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              label="Banjir"
              :value.sync="forms.Banjir"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              label="Puting Beliung"
              :value.sync="forms.PutingBeliung"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              label="Keretakan Tanah"
              :value.sync="forms.KeretakanTanah"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              label="Longsor"
              :value.sync="forms.Longsor"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
            <XSelect
              label="Gempa Bumi"
              :value.sync="forms.GempaBumi"
              :items="[
                { val: '1', txt: 'Pernah' },
                { val: '2', txt: 'Tidak Pernah' },
              ]"
            />
          </div>
        </div>
        <!-- <div v-show="tabId == 'FAQ' || isMobile">
          <Panel
            dbref="PRM.SelWhyPBDTEdit"
            :dbparams="{ NoRef: forms.NoRef }"
            :autobind="false"
            style="padding:10px; font-size:14px;"
          >
            <template v-slot="{ first }">
              <div>Alasan tidak masuk input usulan:</div>
              <div>{{ first.Message }}</div>
            </template>
          </Panel>
        </div> -->
      </div>
    </div>
    <!-- <unsaved-data-prompt :show.sync="showUnsaved" /> -->
  </Modal>
</template>
<script>
import moment from 'moment'
// import UnsavedDataPrompt from './UnsavedDataPrompt.vue'
export default {
  // components: {
  //   UnsavedDataPrompt,
  // },
  data: () => ({
    loadingBDT: false,
    x_show: false,
    loading: false,
    saving: false,
    showUnsaved: false,
    error: '',
    errorAction: '',
    warning: '',
    isBDT: false,

    forms: {},
    tabId: 'Individu',
    disableProgram: true,
    photoGpsTag: null,
  }),
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    noRef: [Object, String, Number],
    nik: [Object, String, Number],
    area: Object,
    disabled: Boolean,
    igahp: Boolean,
  },
  computed: {
    limaTahun() {
      let tahun = []
      for (let i = 0; i < 5; i++) {
        tahun.push({ val: moment().year() - i, txt: moment().year() - i })
      }
      return tahun
    },
  },
  watch: {
    show(val) {
      this.x_show = val
      this.tabId = 'Individu'
      this.error = ''
      this.errorAction = ''
    },
    x_show(val) {
      if (!val) {
        this.forms.RumahDepan = ''
        this.forms.RumahSamping = ''
        this.forms = {}
        this.photoGpsTag = null
      } else this.populate()
      this.$emit('update:show', val)
    },
    'forms.NIK'(val) {
      if (val && val.length == 16) {
        this.CheckDinsos()
      } else {
        this.forms.NewTipeData = null
      }
    },
    'forms.AdaMinat'(val) {
      if (val == '1') {
        this.disableProgram = false
      } else {
        this.disableProgram = true
        this.forms.JenisMinat = null
        this.forms.ProgramPembiayaan = null
        this.forms.BankPelaksana = null
        this.forms.BesarCicilan = null
        this.forms.TahunRencana = null
      }
    },
  },
  methods: {
    async populate() {
      this.saving = false
      let skey = Object.keys(localStorage).find(
        (k) =>
          k.match(
            new RegExp(
              'bnba-' +
                this.noRef?.data
                  ?.map((byte) => byte.toString(16).padStart(2, '0'))
                  .join('') +
                '-'
            )
          ) || k.match(new RegExp('-' + this.nik + '$'))
      )

      const lastData = localStorage.getItem(skey)
      if (lastData) {
        this.forms = JSON.parse(lastData)
        return
      }
      this.loading = true
      this.isBDT = false
      if (this.noRef || this.nik) {
        let { data } = await this.$api.call('PRM_SelPBDTDetail', {
          NoRef: this.noRef,
          NIK: this.nik,
        })
        this.forms = data[0]
      } else {
        this.forms = {
          NIK: '',
          Nama: '',
          Alamat: '',
          IDBDT: '',
          NewTipeData: '',
        }
      }
      this.loading = false
    },
    setTanggalLahir() {
      const nik = this.forms.NIK || this.nik
      if (!this.forms.TanggalLahir) {
        let strdate =
          '19' +
          nik.substr(10, 2) +
          '-' +
          nik.substr(8, 2) +
          '-' +
          nik.substr(6, 2)

        if (moment(strdate).isValid()) this.forms.TanggalLahir = strdate
      }
    },
    async CheckDinsos() {
      if (
        this.forms.NIK &&
        (this.forms.NIK + '').length == 16 &&
        !(200 > this.forms.TipeData || this.isBDT)
      ) {
        let skey = Object.keys(localStorage).find(
          (k) =>
            k.match(
              new RegExp(
                'bnba-' +
                  this.noRef?.data
                    ?.map((byte) => byte.toString(16).padStart(2, '0'))
                    .join('') +
                  '-'
              )
            ) || k.match(new RegExp('-' + this.nik + '$'))
        )

        const lastData = localStorage.getItem(skey)
        if (lastData) {
          if (confirm('Ada data yg belum tersimpan, tampilkan?')) {
            this.forms = JSON.parse(lastData)
            return
          } else {
            localStorage.removeItem(skey)
          }
        }

        this.loadingBDT = true
        let { data } = await this.$api.call('PRM_SelPBDTDetail', {
          NIK: this.forms.NIK,
        })
        if (data.length > 0) {
          this.forms = data?.[0]
          this.setTanggalLahir()
          if (this.forms.TipeData < 200 || this.forms.TipeData > 300) {
            this.loadingBDT = false
            this.isBDT = true
            return
          }
        }

        console.log(this.forms.NIK)
        let d = await this.$api.post(
          this.$api.url + '/api/dinsos/get/' + this.forms.NIK
        )
        this.loadingBDT = false
        if (d.success) {
          if (this.forms.TipeData > 200 && this.forms.TipeData < 300) {
            this.forms.NewTipeData -= new Date().getYear()
          }
          this.isBDT = true

          let alamat =
            (d.data.ALAMAT || d.data.alamat || '').trim() +
            ' RT ' +
            (d.data.RT || d.data.rt || '?') +
            ' RW ' +
            (d.data.RW || d.data.rw || '?')
          this.forms.Nama = (
            this.forms.Nama ||
            d.data.Nama ||
            d.data.nama
          ).trim()
          if (
            alamat.length > this.forms.Alamat ||
            this.forms.Alamat.match(/^ - /)
          ) {
            this.forms.Alamat = alamat
          }

          this.forms.IDBDT = d.data.IDBDT || d.data.id_dtks || d.data.idjtg
          this.forms.NIKBDT = d.data.nik || this.forms.NIK
          this.forms.NamaBDT = (d.data.Nama || d.data.nama).trim()
          this.forms.NewTipeData = new Date().getYear()
        } else if (!data.length) {
          this.$api.notify('Belum terdaftar di BDT', 'error')
        }
        // this.setTanggalLahir()
      }
    },
    async SaveIGAHP(d, withSync = false) {
      await this.$api.call('OUT.SavLenteraHijau', d)
      if (withSync)
        await this.$api.post(this.$api.url + '/api/igahp/sync', {
          NIK: d.NIK,
        })
      this.$api.post(this.$api.url + '/api/igahp/check', {
        nik: d.NIK,
        nama: d.Nama,
      })
    },
    async Save() {
      this.saving = true
      const frms = {
        ...this.forms,
        Kabupaten: this.area.Kabupaten,
        Kecamatan: this.area.Kecamatan,
        Kelurahan: this.area.Kelurahan,
        KodeWilayah: this.area.KodeDagri,
      }
      localStorage.setItem(
        'bnba-' +
          frms.NoRef?.data
            ?.map((byte) => byte.toString(16).padStart(2, '0'))
            .join('') +
          '-' +
          frms.NIK,
        JSON.stringify(frms)
      )
      if (!frms.NIK || !frms.Nama) {
        this.$api.notify('NIK/Nama tidak boleh kosong', 'error')
        return
      }
      this.error = ''
      let ret = await this.$api.call('PRM.SavPBDTDetail', {
        Tahun: null,
        Sumber: null,
        ...frms,
      })
      if (this.igahp) this.SaveIGAHP(frms, !ret.success || !this.forms.NoRef)
      this.saving = false
      if (ret.success) {
        localStorage.removeItem(
          'bnba-' +
            frms.NoRef?.data
              ?.map((byte) => byte.toString(16).padStart(2, '0'))
              .join('') +
            '-' +
            frms.NIK
        )
        // this.$api.post(this.$api.url + '/api/ertlh/update/' + this.forms.NIK)
        this.x_show = false
        this.$emit('save')
      } else {
        this.error = ret.message
        this.errorAction = ret.data[0].ErrAction
      }
    },
    async errorActionYes(act) {
      this.error = ''
      this.errorAction = ''
      let ret = {}

      if (act.match(/pindah desa/)) {
        ret = await this.$api.call('PRM.SavPindahDesa', {
          ...this.area,
          ...this.forms,
        })
      } else if (act.match(/data anda benar/)) {
        ret = await this.$api.call('PRM.SavDoubleNIK', {
          ...this.forms,
        })
      }
      if (!ret.success) {
        this.error = ret.message
        this.errorAction = ret.data[0].ErrAction
      }
    },
    async errorActionNo() {
      this.error = ''
      this.errorAction = ''
    },
    async Delete() {
      if (!confirm(`Anda yakin akan menghapus ${this.forms.Nama}?`)) return

      let ret = await this.$api.call('PRM.DelPBDTedit', {
        ...this.forms,
      })
      if (ret.success) {
        this.x_show = false
        this.$emit('delete')
      } else this.error = ret.message
    },
    updateMapLocation(res) {
      if (res.meta && res.meta.gps && res.meta.gps.lat) {
        this.photoGpsTag = {
          lat: res.meta.gps.lat,
          lon: res.meta.gps.lon,
        }
        this.applyGpsFromPhoto()
      }
    },
    applyGpsFromPhoto() {
      if (this.photoGpsTag) {
        this.forms.GeoLat = this.photoGpsTag.lat
        this.forms.GeoLng = this.photoGpsTag.lon
        this.photoGpsTag = null
      }
    },
    getLocation() {
      if (window.navigator.geolocation) {
        window.navigator.geolocation.getCurrentPosition(
          (pos) => {
            this.forms.GeoLat = pos.coords.latitude
            this.forms.GeoLng = pos.coords.longitude
          },
          (err) => {
            switch (err.code) {
              case err.PERMISSION_DENIED:
                alert('User denied the request for Geolocation.')
                break
              case err.POSITION_UNAVAILABLE:
                alert('Location information is unavailable.')
                break
              case err.TIMEOUT:
                alert('The request to get user location timed out.')
                break
              case err.UNKNOWN_ERROR:
                alert('An unknown error occurred.')
                break
            }
          }
        )
      } else {
        alert('Geolocation is not supported by this browser.')
      }
    },
    async onMapChange() {
      this.forms.RDTR = null
      this.$refs.map.setTooltip('')
      let params = {
        lat: this.forms.GeoLat,
        lon: this.forms.GeoLng,
        kab: this.forms.Kabupaten,
        kec: this.forms.Kecamatan,
      }
      let d = await this.$api.get(
        this.$api.url + '/api/gistaru?' + new URLSearchParams(params).toString()
      )
      let attr = d.data?.features?.[0]?.attributes
      if (attr) {
        this.forms.RDTR = attr.NAMOBJ
        this.$refs.map.setTooltip(
          `<div>${attr.NAMZON || ''}</div><div>${attr.NAMOBJ}</div>`,
          {
            style: {
              color: ['RTH', 'BJ'].includes(attr.KODZON) ? 'white' : 'black',
              backgroundColor: ['RTH', 'BJ'].includes(attr.KODZON)
                ? 'rgba(244, 57, 64, 0.8)'
                : 'rgba(255,255,255,0.7)',
            },
          }
        )
      }
    },
  },
}
</script>
<style lang="scss">
.modal-validasi-detail {
  .ui-dropdown--button {
    width: 250px !important;
  }
  .ui-input {
    .--input {
      width: 250px !important;
    }
  }
  .form-label {
    flex: 0 0 180px;
  }
}
.vert-tab {
  width: 50px;
  height: 50px;
  vertical-align: top;
  cursor: pointer;
  text-align: center;
  border-bottom: 1px solid silver;

  .v-icon {
    margin-top: 8px;
    color: silver;
  }
}

.vert-tab.active {
  background: #f3f3f3;
  position: relative;
  z-index: 2;
  .v-icon {
    color: #333;
  }
}

.info-red {
  padding-top: 8px;
  font-size: 12px;
  color: red;
}

.imgbox {
  width: 150px;
  height: 150px;
  border-right: 1px solid white;
  box-sizing: border-box;
}
</style>
