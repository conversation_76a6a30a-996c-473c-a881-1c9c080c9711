require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
console.log(require('path').resolve(__dirname, '../../.env'))
const fs = require('fs');
const csv = require('csv-parse/sync');
const {stringify} = require('csv-stringify/sync');
const mysql = require('mysql');
const util = require('util');
var readlines = require("n-readlines");

// MySQL Configuration with improved timeout settings
const pool = mysql.createPool({
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '20'),
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3307'),
  // acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  // connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '60000'),
  // timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
  waitForConnections: true,
  queueLimit: 0
});

// Function to get a connection with retry logic
const getConnection = () => {
  return new Promise((resolve, reject) => {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error('Error getting MySQL connection:', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          console.log('Retrying connection in 2 seconds...');
          setTimeout(() => {
            getConnection().then(resolve).catch(reject);
          }, 2000);
        } else {
          reject(err);
        }
      } else {
        // Set session variables to increase timeouts
        connection.query('SET SESSION wait_timeout=300', (err) => {
          if (err) console.error('Error setting wait_timeout:', err);

          connection.query('SET SESSION interactive_timeout=300', (err) => {
            if (err) console.error('Error setting interactive_timeout:', err);
            resolve(connection);
          });
        });
      }
    });
  });
};

// Function to execute a query with retry logic
const executeQuery = async (sql, params, retries = 3) => {
  let connection;
  try {
    connection = await getConnection();
    const query = util.promisify(connection.query).bind(connection);
    return await query(sql, params);
  } catch (error) {
    if (retries > 0 && (error.code === 'PROTOCOL_CONNECTION_LOST' ||
      error.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR' ||
      error.message.includes('timeout'))) {
      console.log(`Query failed, retrying... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      return executeQuery(sql, params, retries - 1);
    }
    throw error;
  } finally {
    if (connection) connection.release();
  }
};

// Process records in batches
async function run(row) {

  // if (row.Kabupaten < 'KOTA MAGELANG') return

  console.log(row.KodeDagri)

  const records = await executeQuery(`update tmp_1022 t 
  join arch_varea2 av 
  ON t.KodeDagri = av.KodeDagri
  set t.Kabupaten = av.Kabupaten,
    t.Kecamatan = av.Kecamatan,
    t.Kelurahan = av.Kelurahan
  where t.KodeDagri =  '${row.KodeDagri}'`);

  return null;
}

const readCSV = async () => {
  const SRC_FILE = 'bnba_kkn.csv'
  console.log(`Reading CSV file: ${SRC_FILE}`);
  // Read CSV file

  var liner = new readlines(SRC_FILE);
  let next = ''
  let start = false
  let i = 0
  let vals = []
  while ((next = liner.next())) {
    i++
    let line = next.toString("ascii").split(",").map(d => d.trim());
    vals.push(`(${line[0] || '0'}, ${mysql.escape(line[1])}, ${mysql.escape(line[2])}, ${mysql.escape(line[3])}, ${mysql.escape(line[4])}, ${mysql.escape(line[5])}, ${mysql.escape(line[6])}, AES_ENCRYPT('${line[0] || 0}', '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5'))`)
    
    if (i % 500 == 0) {
      await executeQuery(`INSERT INTO tmp_bnbakkn (NIK, Nama, Alamat, Kabupaten, Kecamatan, Kelurahan, TipeData, NIKENC) VALUES ` + vals.join(','))
      vals = []
      console.log(i)        
    }
    // if (i> 10) break
  }
  await executeQuery(`INSERT INTO tmp_bnbakkn (NIK, Nama, Alamat, Kabupaten, Kecamatan, Kelurahan, TipeData, NIKENC) VALUES ` + vals.join(','))      
}

async function main() {
  try {

    await readCSV()
    // const records = await executeQuery(`select DISTINCT t.KodeDagri from tmp_1022 t `);

    // // const records = [{ Kabupaten: 'KLATEN' }]
    // // console.log(records)
    // for (const row of records) {
    //   await run(row)
    // }
    console.log('processed successfully');

    // End MySQL pool
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(0);
    });

    process.exit(0)
  } catch (error) {
    console.error('Error:', error);

    // End MySQL pool on error
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(1);
    });
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('Process interrupted, closing connections...');
  pool.end(err => {
    if (err) console.error('Error closing MySQL pool:', err);
    process.exit(2);
  });
});

main();